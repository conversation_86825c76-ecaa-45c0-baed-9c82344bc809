// Simple Animation System
class ModernAnimations {
    constructor() {
        this.init();
    }

    init() {
        console.log('Animations initialized');
        this.setupSimpleEffects();
    }

    // Simple effects setup
    setupSimpleEffects() {
        console.log('Setting up simple effects');
        // Just add some basic hover effects without complex animations
        const cards = document.querySelectorAll('.process-step, .support-area');
        cards.forEach(card => {
            card.addEventListener('mouseenter', () => {
                card.style.transform = 'translateY(-5px)';
            });
            card.addEventListener('mouseleave', () => {
                card.style.transform = 'translateY(0)';
            });
        });
    }

}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    console.log('DOM loaded');
    try {
        new ModernAnimations();
        console.log('Animations initialized successfully');
    } catch (error) {
        console.error('Animation error:', error);
    }
});
