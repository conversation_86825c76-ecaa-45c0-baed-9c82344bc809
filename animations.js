// Modern Animation System
class ModernAnimations {
    constructor() {
        this.init();
    }

    init() {
        this.setupIntersectionObserver();
        this.setupParallaxEffects();
        this.setupHoverEffects();
        this.setupScrollEffects();
    }

    // Intersection Observer for scroll animations
    setupIntersectionObserver() {
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    this.animateElement(entry.target);
                }
            });
        }, observerOptions);

        // Observe all animatable elements
        const animatableElements = document.querySelectorAll(
            '.process-step, .support-area, .service-card, .hero-content, .section-title, .section-subtitle'
        );

        animatableElements.forEach((el, index) => {
            // Add initial state
            el.style.opacity = '0';
            el.style.transform = 'translateY(30px)';
            el.style.transition = 'all 0.6s cubic-bezier(0.4, 0, 0.2, 1)';
            
            // Add delay based on index
            el.style.transitionDelay = `${index * 0.1}s`;
            
            observer.observe(el);
        });
    }

    // Animate element when it comes into view
    animateElement(element) {
        element.style.opacity = '1';
        element.style.transform = 'translateY(0)';
    }

    // Parallax effects for hero section
    setupParallaxEffects() {
        const hero = document.querySelector('.hero');
        if (!hero) return;

        window.addEventListener('scroll', () => {
            const scrolled = window.pageYOffset;
            const rate = scrolled * -0.5;
            
            if (hero.querySelector('.hero::before')) {
                hero.style.transform = `translateY(${rate}px)`;
            }
        });
    }

    // Enhanced hover effects
    setupHoverEffects() {
        // Process steps hover effects
        const processSteps = document.querySelectorAll('.process-step');
        processSteps.forEach(step => {
            step.addEventListener('mouseenter', () => {
                this.addFloatingEffect(step);
            });
            
            step.addEventListener('mouseleave', () => {
                this.removeFloatingEffect(step);
            });
        });

        // Support areas hover effects
        const supportAreas = document.querySelectorAll('.support-area');
        supportAreas.forEach(area => {
            area.addEventListener('mouseenter', () => {
                this.addGlowEffect(area);
            });
            
            area.addEventListener('mouseleave', () => {
                this.removeGlowEffect(area);
            });
        });
    }

    // Add floating animation
    addFloatingEffect(element) {
        element.style.animation = 'float 2s ease-in-out infinite';
    }

    // Remove floating animation
    removeFloatingEffect(element) {
        element.style.animation = '';
    }

    // Add glow effect
    addGlowEffect(element) {
        element.style.boxShadow = '0 20px 40px rgba(59, 130, 246, 0.2), 0 0 20px rgba(59, 130, 246, 0.1)';
    }

    // Remove glow effect
    removeGlowEffect(element) {
        element.style.boxShadow = '';
    }

    // Scroll-based effects
    setupScrollEffects() {
        let ticking = false;

        const updateScrollEffects = () => {
            const scrolled = window.pageYOffset;
            const header = document.querySelector('.header');
            
            // Header background opacity based on scroll
            if (header) {
                const opacity = Math.min(scrolled / 100, 1);
                header.style.background = `rgba(255, 255, 255, ${0.95 + (opacity * 0.05)})`;
            }

            // Process numbers animation
            const processNumbers = document.querySelectorAll('.process-number');
            processNumbers.forEach((number, index) => {
                const rect = number.getBoundingClientRect();
                const isVisible = rect.top < window.innerHeight && rect.bottom > 0;
                
                if (isVisible) {
                    const progress = 1 - (rect.top / window.innerHeight);
                    const rotation = progress * 360;
                    number.style.transform = `rotate(${rotation}deg)`;
                }
            });

            ticking = false;
        };

        const requestScrollUpdate = () => {
            if (!ticking) {
                requestAnimationFrame(updateScrollEffects);
                ticking = true;
            }
        };

        window.addEventListener('scroll', requestScrollUpdate);
    }
}

// Smooth scroll for navigation links
function setupSmoothScroll() {
    const navLinks = document.querySelectorAll('a[href^="#"]');
    
    navLinks.forEach(link => {
        link.addEventListener('click', (e) => {
            e.preventDefault();
            const targetId = link.getAttribute('href');
            const targetElement = document.querySelector(targetId);
            
            if (targetElement) {
                targetElement.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
}

// Stagger animation for grid items
function staggerGridAnimations() {
    const grids = document.querySelectorAll('.grid');
    
    grids.forEach(grid => {
        const items = grid.children;
        Array.from(items).forEach((item, index) => {
            item.style.animationDelay = `${index * 0.1}s`;
        });
    });
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new ModernAnimations();
    setupSmoothScroll();
    staggerGridAnimations();
});

// Add loading animation
window.addEventListener('load', () => {
    document.body.classList.add('loaded');
});
