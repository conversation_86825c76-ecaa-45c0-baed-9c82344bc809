/* Modern CSS Variables */
:root {
    /* Modern Color Palette */
    --primary: #0f172a;
    --secondary: #1e293b;
    --accent: #3b82f6;
    --accent-light: #60a5fa;
    --accent-dark: #1d4ed8;
    --success: #10b981;
    --warning: #f59e0b;

    /* Modern Greys */
    --grey-50: #f8fafc;
    --grey-100: #f1f5f9;
    --grey-200: #e2e8f0;
    --grey-300: #cbd5e1;
    --grey-400: #94a3b8;
    --grey-500: #64748b;
    --grey-600: #475569;
    --grey-700: #334155;
    --grey-800: #1e293b;
    --grey-900: #0f172a;

    /* Semantic Colors */
    --text-primary: var(--grey-900);
    --text-secondary: var(--grey-600);
    --text-light: var(--grey-500);
    --bg-white: #ffffff;
    --bg-light: var(--grey-50);
    --bg-primary: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
    --soft-grey: var(--grey-200);
    --medium-grey: var(--grey-500);
    --dark-grey: var(--grey-800);

    /* Typography */
    --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    --font-heading: 'Playfair Display', Georgia, serif;

    /* Spacing */
    --spacing-xs: 0.5rem;
    --spacing-sm: 1rem;
    --spacing-md: 1.5rem;
    --spacing-lg: 2rem;
    --spacing-xl: 3rem;
    --spacing-2xl: 4rem;

    /* Modern Border Radius */
    --radius-sm: 0.5rem;
    --radius-md: 0.75rem;
    --radius-lg: 1rem;
    --radius-xl: 1.5rem;
    --radius-2xl: 2rem;

    /* Enhanced Shadows */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 10px 10px -5px rgb(0 0 0 / 0.04);
    --shadow-glow: 0 0 20px rgb(59 130 246 / 0.15);

    /* Animation Variables */
    --transition-fast: 0.15s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-normal: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-primary);
    font-size: 1rem;
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--bg-white);
    overflow-x: hidden;
}

/* Modern Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.8;
    }
}

/* Animation Classes */
.animate-fade-in-up {
    animation: fadeInUp 0.6s var(--transition-normal) forwards;
}

.animate-fade-in-left {
    animation: fadeInLeft 0.6s var(--transition-normal) forwards;
}

.animate-fade-in-right {
    animation: fadeInRight 0.6s var(--transition-normal) forwards;
}

.animate-scale-in {
    animation: scaleIn 0.5s var(--transition-normal) forwards;
}

.animate-float {
    animation: float 3s ease-in-out infinite;
}

.animate-pulse {
    animation: pulse 2s ease-in-out infinite;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-heading);
    font-weight: 600;
    line-height: 1.2;
    margin-bottom: var(--spacing-sm);
    color: var(--text-primary);
}

h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; }
h3 { font-size: 1.5rem; }
h4 { font-size: 1.25rem; }

p {
    margin-bottom: var(--spacing-sm);
    color: var(--text-secondary);
}

a {
    color: var(--primary);
    text-decoration: none;
    transition: color 0.3s ease;
}

a:hover {
    color: var(--accent);
}

/* Layout Components */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-md);
}

.section {
    padding: var(--spacing-2xl) 0;
    position: relative;
}

/* Modern section backgrounds */
.section:nth-child(even) {
    background: linear-gradient(135deg, var(--bg-light) 0%, rgba(255, 255, 255, 0.8) 100%);
}

.section:nth-child(even)::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        radial-gradient(circle at 25% 25%, rgba(59, 130, 246, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(59, 130, 246, 0.03) 0%, transparent 50%);
    pointer-events: none;
}

.section-title {
    text-align: center;
    margin-bottom: var(--spacing-md);
    font-size: 2.25rem;
}

.section-subtitle {
    text-align: center;
    font-size: 1.125rem;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xl);
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

/* Grid System */
.grid {
    display: grid;
    gap: var(--spacing-lg);
    align-items: center;
}

/* Grid for process steps - remove center alignment for equal height cards */
.grid.process-grid {
    align-items: stretch;
}

/* Specific grid for process steps to ensure better layout */
.grid-2.process-grid {
    grid-template-columns: repeat(4, 1fr);
}

@media (max-width: 1200px) {
    .grid-2.process-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .grid-2.process-grid {
        grid-template-columns: 1fr;
    }
}

/* Specific grid for support areas to ensure better layout */
.grid-3.support-grid {
    align-items: stretch;
    grid-template-columns: repeat(3, 1fr);
}

@media (max-width: 1024px) {
    .grid-3.support-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .grid-3.support-grid {
        grid-template-columns: 1fr;
    }
}

.grid-2 {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.grid-3 {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

/* Specific grid for expertise cards to ensure 3 columns */
.grid-3.expertise-grid {
    grid-template-columns: repeat(3, 1fr);
}

@media (max-width: 768px) {
    .grid-3.expertise-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 1024px) and (min-width: 769px) {
    .grid-3.expertise-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* Background Utilities */
.bg-light {
    background-color: var(--bg-light);
}

.bg-primary {
    background: linear-gradient(135deg, #1e293b 0%, #334155 100%) !important;
    position: relative;
    min-height: 200px;
    padding: 4rem 0 !important;
}

.bg-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 30% 70%, rgba(59, 130, 246, 0.2) 0%, transparent 50%);
    pointer-events: none;
}

.text-white {
    color: white !important;
}

.text-white h1,
.text-white h2,
.text-white h3,
.text-white h4,
.text-white p {
    color: white !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.text-white .section-title {
    color: white !important;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.text-white .section-subtitle {
    color: rgba(255, 255, 255, 0.95) !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* Ensure CTA section has proper contrast */
.bg-primary.text-white {
    position: relative;
    z-index: 1;
}

.bg-primary.text-white * {
    position: relative;
    z-index: 2;
}

/* Force white text in CTA sections */
.section.bg-primary.text-white h1,
.section.bg-primary.text-white h2,
.section.bg-primary.text-white h3,
.section.bg-primary.text-white h4,
.section.bg-primary.text-white p,
.section.bg-primary.text-white .section-title,
.section.bg-primary.text-white .section-subtitle {
    color: white !important;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5) !important;
}

/* Additional specificity for stubborn text */
.bg-primary .container .section-title,
.bg-primary .container .section-subtitle,
.bg-primary .container h2,
.bg-primary .container p {
    color: #ffffff !important;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.7) !important;
    opacity: 1 !important;
}

/* Ultimate text visibility fix */
section.bg-primary * {
    color: white !important;
}

section.bg-primary h2 {
    color: white !important;
    font-size: 2.5rem !important;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8) !important;
}

section.bg-primary p {
    color: rgba(255, 255, 255, 0.95) !important;
    font-size: 1.2rem !important;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.6) !important;
}

/* Fix button visibility in CTA sections */
.bg-primary .btn-accent {
    background: white !important;
    color: var(--primary) !important;
    border: 2px solid white !important;
    font-weight: 600 !important;
    padding: 1rem 2rem !important;
    font-size: 1.1rem !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;
}

.bg-primary .btn-accent:hover {
    background: var(--accent) !important;
    color: white !important;
    border-color: var(--accent) !important;
    transform: translateY(-3px) !important;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.4) !important;
}

.text-center {
    text-align: center;
}

/* Modern Header and Navigation */
.header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    box-shadow: var(--shadow-md);
    position: sticky;
    top: 0;
    z-index: 1000;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    transition: all var(--transition-normal);
}

.nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-sm) var(--spacing-md);
    max-width: 1200px;
    margin: 0 auto;
    min-height: 60px;
}

.nav-logo h1 {
    font-size: 1.5rem;
    color: var(--primary);
    margin: 0;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: var(--spacing-md);
    align-items: center;
    flex-wrap: nowrap;
}

.nav-link {
    font-weight: 500;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-lg);
    transition: all var(--transition-normal);
    white-space: nowrap;
    font-size: 0.9rem;
    position: relative;
    overflow: hidden;
}

.nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.1), transparent);
    transition: left var(--transition-normal);
    pointer-events: none;
    z-index: -1;
}

.nav-link:hover::before {
    left: 100%;
}

.nav-link:hover,
.nav-link.active {
    background: rgba(59, 130, 246, 0.1);
    color: var(--accent);
    transform: translateY(-1px);
}

.nav-link.cta-button {
    background-color: var(--accent);
    color: white;
    padding: var(--spacing-xs) var(--spacing-md);
}

.nav-link.cta-button:hover {
    background-color: var(--primary);
    color: white;
}

.nav-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.nav-toggle span {
    width: 25px;
    height: 3px;
    background-color: var(--primary);
    margin: 3px 0;
    transition: 0.3s;
}

/* Modern Buttons */
.btn {
    display: inline-block;
    padding: var(--spacing-sm) var(--spacing-lg);
    border-radius: var(--radius-lg);
    font-weight: 600;
    text-align: center;
    text-decoration: none;
    transition: all var(--transition-normal);
    border: none;
    cursor: pointer;
    font-size: 1rem;
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left var(--transition-slow);
    pointer-events: none;
    z-index: -1;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: linear-gradient(135deg, var(--accent) 0%, var(--accent-dark) 100%);
    color: white;
    box-shadow: var(--shadow-md);
}

.btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-xl);
    background: linear-gradient(135deg, var(--accent-light) 0%, var(--accent) 100%);
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.1);
    color: var(--primary);
    border: 2px solid var(--accent);
    backdrop-filter: blur(10px);
}

.btn-secondary:hover {
    background: var(--accent);
    color: white;
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-accent {
    background: rgba(255, 255, 255, 0.95);
    color: var(--primary);
    box-shadow: var(--shadow-md);
}

.btn-accent:hover {
    background: white;
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
}

/* Modern Hero Section */
.hero {
    padding: var(--spacing-2xl) 0;
    background: linear-gradient(135deg, var(--bg-light) 0%, white 100%);
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(59, 130, 246, 0.05) 0%, transparent 50%);
    pointer-events: none;
}

.hero .container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-xl);
    align-items: center;
}

.hero-title {
    font-size: 3rem;
    margin-bottom: var(--spacing-md);
    color: var(--primary);
}

.hero-subtitle {
    font-size: 1.25rem;
    margin-bottom: var(--spacing-xl);
    color: var(--text-secondary);
}

.hero-image {
    border-radius: var(--radius-lg);
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
}

.hero-img {
    width: 100%;
    height: auto;
    max-height: 400px;
    object-fit: cover;
    border-radius: var(--radius-lg);
}

.content-image {
    border-radius: var(--radius-lg);
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
}

.section-img {
    width: 100%;
    height: auto;
    max-height: 350px;
    object-fit: cover;
    border-radius: var(--radius-lg);
}

.image-placeholder {
    background-color: var(--soft-grey);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xl);
    text-align: center;
    color: var(--text-light);
    min-height: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Service Cards */
.service-card {
    background-color: white;
    padding: var(--spacing-xl);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    text-align: center;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.service-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.service-icon {
    font-size: 3rem;
    margin-bottom: var(--spacing-md);
}

.service-card h3 {
    color: var(--primary);
    margin-bottom: var(--spacing-sm);
}

/* Checklist */
.checklist {
    list-style: none;
}

.checklist li {
    position: relative;
    padding-left: var(--spacing-lg);
    margin-bottom: var(--spacing-sm);
    color: var(--text-secondary);
}

.checklist li::before {
    content: "✓";
    position: absolute;
    left: 0;
    color: var(--accent);
    font-weight: bold;
}

/* Features */
.feature {
    margin-bottom: var(--spacing-lg);
}

.feature h3 {
    color: var(--primary);
    margin-bottom: var(--spacing-xs);
}

/* Footer */
.footer {
    background-color: var(--dark-grey);
    color: white;
    padding: var(--spacing-2xl) 0 var(--spacing-lg);
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-lg);
}

.footer-section h3,
.footer-section h4 {
    color: white;
    margin-bottom: var(--spacing-sm);
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: var(--spacing-xs);
}

.footer-section a {
    color: var(--medium-grey);
}

.footer-section a:hover {
    color: white;
}

.footer-bottom {
    border-top: 1px solid var(--medium-grey);
    padding-top: var(--spacing-sm);
    text-align: center;
    color: var(--medium-grey);
}

/* Quote Styles */
.quote {
    background-color: var(--bg-light);
    border-left: 4px solid var(--accent);
    padding: var(--spacing-lg);
    margin: var(--spacing-xl) 0;
    font-style: italic;
    font-size: 1.125rem;
    color: var(--text-primary);
    border-radius: var(--radius-md);
}

/* Expertise Cards */
.expertise-card {
    background-color: white;
    padding: var(--spacing-lg);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    text-align: center;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
}

.expertise-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-md);
}

.expertise-icon {
    font-size: 2.5rem;
    margin-bottom: var(--spacing-sm);
}

.expertise-card h3 {
    color: var(--primary);
    margin-bottom: var(--spacing-xs);
    font-size: 1.125rem;
    min-height: 2.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.expertise-card p {
    font-size: 0.9rem;
    color: var(--text-light);
    flex-grow: 1;
    display: flex;
    align-items: center;
}

/* Commitment Items */
.commitment-item {
    margin-bottom: var(--spacing-lg);
    padding: var(--spacing-md);
    border-radius: var(--radius-md);
    background-color: var(--bg-light);
}

.commitment-item h3 {
    color: var(--primary);
    margin-bottom: var(--spacing-xs);
    font-size: 1.125rem;
}

.commitment-item p {
    color: var(--text-secondary);
    margin: 0;
}

/* Modern Process Steps */
.process-step {
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    padding: var(--spacing-lg);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
    text-align: center;
    position: relative;
    transition: all var(--transition-normal);
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    border: 1px solid rgba(255, 255, 255, 0.2);
    overflow: hidden;
}

.process-step::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--accent) 0%, var(--accent-light) 100%);
    transform: scaleX(0);
    transition: transform var(--transition-normal);
}

@media (min-width: 1201px) {
    .process-step {
        padding: var(--spacing-md);
    }
}

@media (max-width: 1200px) {
    .process-step {
        padding: var(--spacing-xl);
    }
}

.process-step:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: var(--shadow-xl);
    background: rgba(255, 255, 255, 0.95);
}

.process-step:hover::before {
    transform: scaleX(1);
}

.process-number {
    background: linear-gradient(135deg, var(--accent) 0%, var(--accent-dark) 100%);
    color: white;
    width: 3rem;
    height: 3rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 1.25rem;
    margin: 0 auto var(--spacing-md);
    box-shadow: var(--shadow-md);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.process-number::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, var(--accent-light) 0%, var(--accent) 100%);
    opacity: 0;
    transition: opacity var(--transition-normal);
}

.process-step:hover .process-number::before {
    opacity: 1;
}

.process-number span {
    position: relative;
    z-index: 1;
}

.process-icon {
    font-size: 3rem;
    margin-bottom: var(--spacing-md);
}

@media (min-width: 1201px) {
    .process-number {
        width: 2.5rem;
        height: 2.5rem;
        font-size: 1rem;
        margin-bottom: var(--spacing-sm);
    }

    .process-icon {
        font-size: 2.5rem;
        margin-bottom: var(--spacing-sm);
    }
}

.process-step h3 {
    color: var(--primary);
    margin-bottom: var(--spacing-sm);
}

.process-step h4 {
    color: var(--text-secondary);
    font-size: 1rem;
    font-weight: 500;
    margin-bottom: var(--spacing-sm);
}

.process-list {
    list-style: none;
    text-align: left;
    margin-top: var(--spacing-md);
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
}

.process-list li {
    position: relative;
    padding-left: var(--spacing-md);
    margin-bottom: var(--spacing-xs);
    color: var(--text-secondary);
}

.process-list li::before {
    content: "•";
    position: absolute;
    left: 0;
    color: var(--accent);
    font-weight: bold;
}

/* Role Cards */
.role-card {
    background-color: white;
    padding: var(--spacing-xl);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
}

.role-card h3 {
    color: var(--primary);
    margin-bottom: var(--spacing-md);
    text-align: center;
}

.role-list {
    list-style: none;
}

.role-list li {
    position: relative;
    padding-left: var(--spacing-md);
    margin-bottom: var(--spacing-sm);
    color: var(--text-secondary);
}

.role-list li::before {
    content: "✓";
    position: absolute;
    left: 0;
    color: var(--accent);
    font-weight: bold;
}

.partnership-statement {
    font-size: 1.125rem;
    color: var(--primary);
    font-weight: 500;
    max-width: 800px;
    margin: 0 auto;
    padding: var(--spacing-lg);
    background-color: white;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
}

/* Modern Support Areas */
.support-area {
    text-align: center;
    padding: var(--spacing-lg);
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
    transition: all var(--transition-normal);
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    border: 1px solid rgba(255, 255, 255, 0.2);
    position: relative;
    overflow: hidden;
}

.support-area::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(59, 130, 246, 0.02) 100%);
    opacity: 0;
    transition: opacity var(--transition-normal);
}

.support-area:hover {
    transform: translateY(-5px) scale(1.02);
    box-shadow: var(--shadow-xl);
    background: rgba(255, 255, 255, 0.95);
}

.support-area:hover::before {
    opacity: 1;
}

.support-icon {
    font-size: 2.5rem;
    margin-bottom: var(--spacing-sm);
}

.support-area h3 {
    color: var(--primary);
    margin-bottom: var(--spacing-xs);
    font-size: 1.125rem;
}

.support-area p {
    color: var(--text-secondary);
    font-size: 0.9rem;
    flex-grow: 1;
    display: flex;
    align-items: center;
    margin: 0;
}

/* Criteria Cards */
.criteria-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-lg);
    margin-top: var(--spacing-xl);
}

.criteria-card {
    background-color: white;
    padding: var(--spacing-xl);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    text-align: center;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.criteria-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.criteria-icon {
    font-size: 3rem;
    margin-bottom: var(--spacing-md);
}

.criteria-card h3 {
    color: var(--primary);
    margin-bottom: var(--spacing-sm);
    font-size: 1.25rem;
}

.criteria-card p {
    color: var(--text-secondary);
    margin: 0;
}

/* Business Types */
.business-types {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
    margin-top: var(--spacing-xl);
}

.business-category {
    background-color: white;
    padding: var(--spacing-lg);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
}

.business-category h3 {
    color: var(--primary);
    margin-bottom: var(--spacing-md);
    text-align: center;
    font-size: 1.125rem;
}

.business-list {
    list-style: none;
}

.business-list li {
    position: relative;
    padding-left: var(--spacing-md);
    margin-bottom: var(--spacing-xs);
    color: var(--text-secondary);
}

.business-list li::before {
    content: "•";
    position: absolute;
    left: 0;
    color: var(--accent);
    font-weight: bold;
}

/* Quality Items */
.qualities-content {
    padding-right: var(--spacing-lg);
}

.quality-item {
    margin-bottom: var(--spacing-lg);
    padding: var(--spacing-md);
    border-left: 4px solid var(--accent);
    background-color: var(--bg-light);
    border-radius: 0 var(--radius-md) var(--radius-md) 0;
}

.quality-item h3 {
    color: var(--primary);
    margin-bottom: var(--spacing-xs);
    font-size: 1.125rem;
}

.quality-item p {
    color: var(--text-secondary);
    margin: 0;
}

/* Self Assessment */
.self-assessment {
    max-width: 800px;
    margin: 0 auto;
}

.assessment-intro {
    text-align: center;
    margin-bottom: var(--spacing-xl);
}

.assessment-intro p {
    font-size: 1.125rem;
    color: var(--text-primary);
}

.assessment-checklist {
    background-color: white;
    padding: var(--spacing-xl);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
}

.assessment-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: var(--spacing-lg);
    padding: var(--spacing-md);
    border-radius: var(--radius-md);
    transition: background-color 0.3s ease;
}

.assessment-item:hover {
    background-color: var(--bg-light);
}

.check-icon {
    background-color: var(--accent);
    color: white;
    width: 1.5rem;
    height: 1.5rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 0.875rem;
    margin-right: var(--spacing-md);
    flex-shrink: 0;
    margin-top: 0.125rem;
}

.assessment-item p {
    color: var(--text-primary);
    margin: 0;
    font-size: 1rem;
}

/* Differentiators */
.differentiators {
    margin-top: var(--spacing-xl);
}

.differentiator {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-xl);
    align-items: center;
    margin-bottom: var(--spacing-2xl);
    padding: var(--spacing-xl);
    border-radius: var(--radius-lg);
    background-color: var(--bg-light);
}

.differentiator.reverse {
    background-color: white;
    box-shadow: var(--shadow-sm);
}

.differentiator.reverse .diff-content {
    order: 2;
}

.differentiator.reverse .diff-image {
    order: 1;
}

.diff-icon {
    font-size: 3rem;
    margin-bottom: var(--spacing-md);
}

.diff-content h3 {
    color: var(--primary);
    margin-bottom: var(--spacing-md);
    font-size: 1.5rem;
}

.diff-content p {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-md);
    font-size: 1.125rem;
}

.diff-benefits {
    list-style: none;
}

.diff-benefits li {
    position: relative;
    padding-left: var(--spacing-md);
    margin-bottom: var(--spacing-xs);
    color: var(--text-secondary);
}

.diff-benefits li::before {
    content: "✓";
    position: absolute;
    left: 0;
    color: var(--accent);
    font-weight: bold;
}

/* Comparison Table */
.comparison-table {
    background-color: white;
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-md);
}

.comparison-header,
.comparison-row {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr 1fr;
}

.comparison-header {
    background-color: var(--primary);
    color: white;
    font-weight: 600;
}

.comparison-header .comparison-category {
    background-color: var(--dark-grey);
}

.comparison-column,
.comparison-cell,
.comparison-category {
    padding: var(--spacing-md);
    text-align: center;
    border-right: 1px solid var(--soft-grey);
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 60px;
}

.comparison-column:last-child,
.comparison-cell:last-child {
    border-right: none;
}

.comparison-row {
    border-bottom: 1px solid var(--soft-grey);
}

.comparison-row:last-child {
    border-bottom: none;
}

.comparison-row:nth-child(even) {
    background-color: var(--bg-light);
}

.comparison-category {
    background-color: var(--soft-grey);
    font-weight: 600;
    color: var(--text-primary);
    justify-content: flex-start;
    padding-left: var(--spacing-lg);
}

.highlight {
    background-color: var(--accent) !important;
    color: white !important;
    font-weight: 600;
}

/* Legacy Partners header styling */
.comparison-header .highlight {
    font-family: var(--font-heading) !important;
    font-size: 1.1rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.3);
}

/* Success Factors */
.success-factor {
    text-align: center;
    padding: var(--spacing-lg);
    background-color: white;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.success-factor:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-md);
}

.factor-icon {
    font-size: 2.5rem;
    margin-bottom: var(--spacing-sm);
}

.success-factor h3 {
    color: var(--primary);
    margin-bottom: var(--spacing-xs);
    font-size: 1.125rem;
}

.success-factor p {
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin: 0;
}

/* Impact Cards */
.impact-card {
    background-color: white;
    padding: var(--spacing-xl);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.impact-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.impact-icon {
    font-size: 3rem;
    margin-bottom: var(--spacing-md);
    text-align: center;
}

.impact-card h3 {
    color: var(--primary);
    margin-bottom: var(--spacing-sm);
    text-align: center;
}

.impact-card p {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-md);
    text-align: center;
}

.impact-benefits {
    list-style: none;
    text-align: left;
}

.impact-benefits li {
    position: relative;
    padding-left: var(--spacing-md);
    margin-bottom: var(--spacing-xs);
    color: var(--text-secondary);
}

.impact-benefits li::before {
    content: "✓";
    position: absolute;
    left: 0;
    color: var(--accent);
    font-weight: bold;
}

/* Metrics Grid */
.metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
    margin-top: var(--spacing-xl);
}

.metric-card {
    background-color: white;
    padding: var(--spacing-xl);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    text-align: center;
    transition: transform 0.3s ease;
}

.metric-card:hover {
    transform: translateY(-3px);
}

.metric-number {
    font-size: 3rem;
    font-weight: 700;
    color: var(--accent);
    margin-bottom: var(--spacing-xs);
    font-family: var(--font-heading);
}

.metric-label {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--primary);
    margin-bottom: var(--spacing-sm);
}

.metric-card p {
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin: 0;
}

/* Timeline */
.journey-timeline {
    position: relative;
    max-width: 800px;
    margin: 0 auto;
}

.journey-timeline::before {
    content: '';
    position: absolute;
    left: 2rem;
    top: 0;
    bottom: 0;
    width: 2px;
    background-color: var(--accent);
}

.timeline-item {
    position: relative;
    margin-bottom: var(--spacing-xl);
    padding-left: 5rem;
}

.timeline-marker {
    position: absolute;
    left: 1rem;
    top: 0;
    width: 2rem;
    height: 2rem;
    background-color: var(--accent);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 0.875rem;
}

.timeline-content {
    background-color: white;
    padding: var(--spacing-lg);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
}

.timeline-content h3 {
    color: var(--primary);
    margin-bottom: var(--spacing-sm);
}

.timeline-content p {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-sm);
}

.timeline-duration {
    font-size: 0.875rem;
    color: var(--accent);
    font-weight: 600;
}

/* Future Stories */
.future-stories {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
    margin-top: var(--spacing-xl);
}

.story-placeholder {
    background-color: white;
    padding: var(--spacing-xl);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    text-align: center;
    border: 2px dashed var(--soft-grey);
    transition: border-color 0.3s ease;
}

.story-placeholder:hover {
    border-color: var(--accent);
}

.story-icon {
    font-size: 3rem;
    margin-bottom: var(--spacing-md);
}

.story-placeholder h3 {
    color: var(--primary);
    margin-bottom: var(--spacing-sm);
}

.story-placeholder p {
    color: var(--text-light);
    font-size: 0.9rem;
    margin: 0;
}

.future-message {
    font-size: 1.125rem;
    color: var(--text-primary);
    font-style: italic;
    max-width: 600px;
    margin: 0 auto;
    padding: var(--spacing-lg);
    background-color: white;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
}

/* Contact Page Styles */
.contact-wrapper {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: var(--spacing-2xl);
    align-items: start;
}

.contact-form-section h2 {
    color: var(--primary);
    margin-bottom: var(--spacing-sm);
}

.contact-form-section > p {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xl);
}

/* Form Styles */
.contact-form {
    background-color: white;
    padding: var(--spacing-xl);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
}

.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-group label {
    display: block;
    margin-bottom: var(--spacing-xs);
    font-weight: 500;
    color: var(--text-primary);
}

.form-group input,
.form-group textarea,
.form-group select {
    width: 100%;
    padding: var(--spacing-sm);
    border: 2px solid var(--soft-grey);
    border-radius: var(--radius-md);
    font-size: 1rem;
    font-family: var(--font-primary);
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    outline: none;
    border-color: var(--accent);
    box-shadow: 0 0 0 3px rgba(217, 119, 6, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 100px;
}

.checkbox-group {
    display: flex;
    align-items: flex-start;
}

.checkbox-label {
    display: flex;
    align-items: flex-start;
    cursor: pointer;
    font-size: 0.9rem;
    color: var(--text-secondary);
}

.checkbox-label input[type="checkbox"] {
    width: auto;
    margin-right: var(--spacing-sm);
    margin-top: 0.125rem;
}

.form-submit {
    width: 100%;
    padding: var(--spacing-md);
    font-size: 1.125rem;
}

/* Form Success */
.form-success {
    background-color: white;
    padding: var(--spacing-xl);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    text-align: center;
}

.success-icon {
    background-color: var(--accent);
    color: white;
    width: 4rem;
    height: 4rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    font-weight: bold;
    margin: 0 auto var(--spacing-md);
}

.form-success h3 {
    color: var(--primary);
    margin-bottom: var(--spacing-sm);
}

.form-success p {
    color: var(--text-secondary);
    margin: 0;
}

/* Contact Info Section */
.contact-info-section {
    background-color: var(--bg-light);
    padding: var(--spacing-xl);
    border-radius: var(--radius-lg);
    height: fit-content;
}

.contact-info-section h3 {
    color: var(--primary);
    margin-bottom: var(--spacing-lg);
}

.contact-method {
    display: flex;
    align-items: flex-start;
    margin-bottom: var(--spacing-lg);
}

.contact-icon {
    font-size: 1.5rem;
    margin-right: var(--spacing-md);
    margin-top: 0.25rem;
}

.contact-details h4 {
    color: var(--primary);
    margin-bottom: var(--spacing-xs);
    font-size: 1rem;
}

.contact-details p {
    color: var(--text-secondary);
    margin: 0;
    font-size: 0.9rem;
}

.contact-details a {
    color: var(--primary);
    text-decoration: none;
}

.contact-details a:hover {
    color: var(--accent);
}

/* What to Expect */
.what-to-expect {
    margin-top: var(--spacing-xl);
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--soft-grey);
}

.what-to-expect h3 {
    color: var(--primary);
    margin-bottom: var(--spacing-md);
}

.expectation-step {
    display: flex;
    align-items: flex-start;
    margin-bottom: var(--spacing-md);
}

.step-number {
    background-color: var(--accent);
    color: white;
    width: 1.5rem;
    height: 1.5rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 0.875rem;
    margin-right: var(--spacing-sm);
    flex-shrink: 0;
    margin-top: 0.125rem;
}

.expectation-step p {
    color: var(--text-secondary);
    margin: 0;
    font-size: 0.9rem;
}

/* FAQ Styles */
.faq-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--spacing-lg);
    margin-top: var(--spacing-xl);
}

.faq-item {
    background-color: white;
    padding: var(--spacing-lg);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
}

.faq-item h3 {
    color: var(--primary);
    margin-bottom: var(--spacing-sm);
    font-size: 1.125rem;
}

.faq-item p {
    color: var(--text-secondary);
    margin: 0;
    font-size: 0.9rem;
    line-height: 1.6;
}

/* Medium screens - adjust navigation spacing */
@media (max-width: 1024px) {
    .nav-menu {
        gap: var(--spacing-sm);
    }

    .nav-link {
        padding: var(--spacing-xs);
        font-size: 0.85rem;
    }

    .nav-link.cta-button {
        padding: var(--spacing-xs) var(--spacing-sm);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-menu {
        display: none;
    }

    .nav-toggle {
        display: flex;
    }

    .hero .container {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .hero-title {
        font-size: 2rem;
    }

    .section-title {
        font-size: 1.75rem;
    }

    .grid-2,
    .grid-3 {
        grid-template-columns: 1fr;
    }

    .container {
        padding: 0 var(--spacing-sm);
    }

    .contact-wrapper {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }

    .differentiator,
    .differentiator.reverse {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .differentiator.reverse .diff-content,
    .differentiator.reverse .diff-image {
        order: unset;
    }

    .comparison-header,
    .comparison-row {
        grid-template-columns: 1fr;
        gap: var(--spacing-xs);
    }

    .comparison-column,
    .comparison-cell,
    .comparison-category {
        text-align: left;
        border-right: none;
        border-bottom: 1px solid var(--soft-grey);
    }

    .timeline-item {
        padding-left: 3rem;
    }

    .journey-timeline::before {
        left: 1rem;
    }

    .timeline-marker {
        left: 0.5rem;
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 1.75rem;
    }

    .section-title {
        font-size: 1.5rem;
    }

    .btn {
        padding: var(--spacing-sm);
        font-size: 0.9rem;
    }
}
